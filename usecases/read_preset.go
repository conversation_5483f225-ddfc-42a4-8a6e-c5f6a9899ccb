package usecases

import (
	"context"

	"github.com/google/uuid"
)

type PresetRetriever struct {
	presetRepo presetRepositoryReplica
}

func NewPresetRetriever(presetRepo presetRepositoryReplica) *PresetRetriever {
	if IsNil(presetRepo) {
		panic("presetRepo cannot be nil")
	}
	return &PresetRetriever{presetRepo: presetRepo}
}

func (dr *PresetRetriever) RetrievePreset(ctx context.Context, presenter PresetPresenter, presetId uuid.UUID) {
	preset, err := dr.presetRepo.ReadPreset(ctx, presetId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentPreset(ctx, preset)
}

func (dr *PresetRetriever) FindPresetByTemplateId(ctx context.Context, presenter PresetPresenter, templateId string) {
	preset, err := dr.presetRepo.FindPresetByTemplateId(ctx, templateId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentPreset(ctx, preset)
}
