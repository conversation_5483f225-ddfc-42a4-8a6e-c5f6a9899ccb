package usecases

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type Preset struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	RoomLayout   json.RawMessage    `json:"room_layout"`
	Measurements json.RawMessage    `json:"measurements"`
	Design       Design             `json:"design"`
	Rendition    entities.Rendition `json:"rendition"`
}
