package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

// TODO: move memStore to a separate package that can be shared with other tests.

type project struct {
	designs         []adapters.Design
	currentDesignId string
}

type memStore struct {
	projects map[entities.ProjectId]*project
}

func newMemStore() *memStore {
	m := memStore{
		projects: make(map[entities.ProjectId]*project),
	}
	m.projects[projId] = &project{}
	return &m
}

func (m *memStore) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	results, ok := m.projects[projectId]
	if !ok {
		fmt.Printf("Project %s not found in store", projectId)
		return nil, fmt.Errorf("project %s not found", projectId)
	}
	return results.designs, nil
}

func (m *memStore) DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, []error, error) {
	results := make(map[entities.ProjectId][]adapters.Design)
	errors := []error{}
	for _, projectId := range projectIds {
		designs, err := m.DesignsForProject(ctx, projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (m *memStore) addDesignToProject(projectId entities.ProjectId, design adapters.Design) error {
	project, ok := m.projects[projectId]
	if !ok {
		return fmt.Errorf("project %s not found", projectId)
	}
	project.designs = append(project.designs, design)
	return nil
}

func (m *memStore) UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error {
	project, ok := m.projects[projectId]
	if !ok {
		return fmt.Errorf("project %s not found", projectId)
	}
	project.designs = designs
	return nil
}

func (m *memStore) MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error {
	return nil
}

func (m *memStore) IdsOfProjectsWithDesigns(_ context.Context) ([]entities.ProjectId, error) {
	var projectIds []entities.ProjectId
	for projectId := range m.projects {
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

func (m *memStore) GetCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId) (string, error) {
	proj, ok := m.projects[projectId]
	if !ok {
		return "", fmt.Errorf("no current design ID set for project %s", projectId)
	}
	return proj.currentDesignId, nil
}

func (m *memStore) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	proj, ok := m.projects[projectId]
	if !ok {
		return fmt.Errorf("no current design ID set for project %s", projectId)
	}
	proj.currentDesignId = designId.String()
	return nil
}

const (
	projId             = "PRJ-FOOBAR"
	jsonSchemaFilename = "room-design.schema.json"
)

func setup(t *testing.T) (*web.DesignMutationHandler, *web.HttpGetReqHandler, *memStore) {
	t.Helper()
	logger := slog.Default()
	schema := controllers.Schema(jsonSchemaFilename)
	m := newMemStore()
	ai := gateways.NewFakeLLM()
	queryController := controllers.NewDesignAccessController(m, logger)
	readHandler := web.NewHttpGetReqHandler(logger, queryController)
	cmdController := controllers.NewDesignMutationController(m, ai, logger)
	writeHandler := web.NewDesignMutationHandler(logger, schema, cmdController)
	return writeHandler, readHandler, m
}

func TestAddingDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup(t)
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	req := httptest.NewRequest("POST", fmt.Sprintf("/projects/%s/designs", projId), bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePost(recorder, req)
	if status := recorder.Code; status != http.StatusCreated {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	storedDesigns := memStore.projects[projId].designs
	if got, want := len(storedDesigns), 1; got != want {
		t.Fatalf("wrong number of stored designs: got %d want %d", got, want)
	}
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/%s/designs", projId), nil)
	req.SetPathValue("projectId", projId)
	recorder = httptest.NewRecorder()
	readHandler.HandleListDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	storedDesign := storedDesigns[0]
	testDesign.ID = storedDesign.ID
	testDesign.Title = storedDesign.Title
	testDesign.Description = storedDesign.Description
	data, err = json.Marshal([]adapters.Design{testDesign})
	if err != nil {
		t.Fatal(err)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestModifyingDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	if err := memStore.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	lastUpdatedDateTime := "2025-06-13T01:00:59.999999Z"
	testDesign.LastUpdatedDateTime = &lastUpdatedDateTime
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePut(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
	if err := json.Unmarshal(data, &testDesign); err != nil {
		t.Fatal(err)
	}
	if testDesign.LastUpdatedDateTime == nil {
		t.Error("lastUpdatedDateTime not set")
	}
}

func TestDeletingDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	if err := memStore.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandleDelete(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusNotFound {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNotFound)
	}
}

func TestFetchingDesignsForMultipleProjects(t *testing.T) {
	const paramName = "ids"
	_, readHandler, memStore := setup(t)
	memStore.projects["PRJ-FOOBAR2"] = &project{}
	projId2 := entities.NewProjectId("PRJ-FOOBAR2")
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	if err := memStore.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	if err := memStore.addDesignToProject(projId2, testDesign2); err != nil {
		t.Fatal(err)
	}
	params := url.Values{}
	params.Add(paramName, fmt.Sprintf("%s,%s", projId, projId2))
	req := httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder := httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}

	params.Set(paramName, fmt.Sprintf("%s,%s", projId, entities.NewProjectId("PRJ-PHANTOM")))
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
}

func TestPutAllDesignsForProject(t *testing.T) {
	writeHandler, _, memStore := setup(t)
	testDesign1 := genDesign()
	testDesign1.ID = uuid.NewString()
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	data, err := json.Marshal([]adapters.Design{testDesign1, testDesign2})
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs", projId)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePutAllDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := memStore.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, want)
	}
}

func TestPutAllDesignsForProjectLeavesExistingDesignsIntactWhenRejectingInvalidPayload(t *testing.T) {
	writeHandler, _, memStore := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	memStore.addDesignToProject(projId, testDesign)
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	memStore.addDesignToProject(projId, testDesign2)
	url := fmt.Sprintf("/projects/%s/designs", projId)
	req := httptest.NewRequest("PUT", url, bytes.NewReader([]byte("[{},{}]")))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePutAllDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusBadRequest)
	}
	designs, err := memStore.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, want)
	}
}
