-- migrate:up
CREATE SCHEMA design;
SET search_path TO public,
    design;
CREATE TYPE design.color_scheme_enum AS ENUM ('Neutral', 'Bold');
CREATE TYPE design.style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);
CREATE TYPE design.tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);
CREATE TYPE design.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);
CREATE TABLE design.room_designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    project_id TEXT,
    status TEXT,
    title TEXT,
    description TEXT,
    color_scheme color_scheme_enum,
    style style_enum
);
CREATE INDEX ON design.room_designs (project_id);
CREATE TABLE design.default_products (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    floor_tile UUID,
    floor_tile_pattern tile_pattern_enum,
    paint UUID,
    toilet UUID,
    vanity UUID,
    faucet UUID,
    mirror UUID,
    lighting UUID,
    shelving UUID,
    wall_tile_placement TEXT NOT NULL,
    wall_tile UUID,
    wall_tile_pattern tile_pattern_enum,
    wallpaper_placement TEXT NOT NULL,
    wallpaper UUID,
    shower_system UUID,
    shower_floor_tile UUID,
    shower_floor_tile_pattern tile_pattern_enum,
    shower_wall_tile UUID,
    shower_wall_tile_pattern tile_pattern_enum,
    shower_short_wall_tile UUID,
    shower_glass UUID,
    niche_tile UUID,
    tub UUID,
    tub_filler UUID,
    tub_door UUID
);
CREATE TABLE design.render_prefs (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    shower_glass_visible BOOLEAN NOT NULL DEFAULT FALSE,
    tub_door_visible BOOLEAN NOT NULL DEFAULT FALSE,
    niches_visible BOOLEAN NOT NULL DEFAULT FALSE
);
CREATE DOMAIN posint AS integer CHECK (VALUE > 0);
CREATE TABLE design.retail_info (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    total_price_cents posint,
    lead_time_days posint,
    sku_count posint
);
CREATE DOMAIN http_img_url AS text CHECK (VALUE ~ '^https?://.*\.(webp|jpe?g|png)$');
CREATE TABLE design.renditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    room_layout_xxhash BYTEA NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status rendition_status_enum NOT NULL,
    url http_img_url,
    CONSTRAINT chk_rendition_url CHECK (
        status <> 'Completed'
        OR url IS NOT NULL
    )
);
CREATE INDEX ON design.renditions (room_design_id);
-- migrate:down
DROP SCHEMA IF EXISTS design CASCADE;