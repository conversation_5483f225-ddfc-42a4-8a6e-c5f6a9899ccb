-- migrate:up
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION design.maintain_updated_at_column() R<PERSON><PERSON>NS TRIGGER AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$LANGUAGE plpgsql;
CREATE TRIGGER refresh_designs_updated_at BEFORE
UPDATE ON design.room_designs FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();
CREATE TRIGGER refresh_renditions_updated_at BEFORE
UPDATE ON design.renditions FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();
-- migrate:down
DROP TRIGGER IF EXISTS refresh_designs_updated_at ON design.room_designs;
DROP TRIGGER IF EXISTS refresh_renditions_updated_at ON design.renditions;
DROP FUNCTION IF EXISTS design.maintain_updated_at_column();