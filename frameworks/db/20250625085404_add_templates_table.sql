-- migrate:up
CREATE TABLE design.templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    color_scheme design.color_scheme_enum NOT NULL,
    style design.style_enum NOT NULL,
    render_priority posint,
    name TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    atmosphere TEXT[] NOT NULL,
    color_palette TEXT[] NOT NULL,
    material_palette TEXT[] NOT NULL,
    inspiration TEXT NOT NULL,
    highlighted_brand_urls http_img_url[],
    rendition_id UUID UNIQUE NOT NULL REFERENCES design.renditions (id) ON DELETE CASCADE
);
CREATE TRIGGER refresh_templates_updated_at BEFORE
UPDATE ON design.templates FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();

CREATE TABLE design.template_options (
    template_id UUID PRIMARY KEY REFERENCES design.templates (id) ON DELETE CASCADE,
    alcove_tub UUID NOT NULL,
    freestanding_tub UUID NOT NULL,
    shower_glass_fixed UUID NOT NULL,
    shower_glass_sliding UUID NOT NULL,
    shower_system_full UUID NOT NULL,
    shower_system_shower UUID NOT NULL,
    tub_door_fixed UUID NOT NULL,
    tub_door_sliding UUID NOT NULL
);

CREATE TABLE design.vanity_scaling_options (
    template_id UUID NOT NULL REFERENCES design.templates (id) ON DELETE CASCADE,
    min_vanity_length_inches posint NOT NULL,
    UNIQUE (template_id, min_vanity_length_inches),
    vanity_product_id UUID NOT NULL,
    faucet_product_id UUID NOT NULL
);

CREATE TABLE design.template_provenance (
    template_id UUID PRIMARY KEY REFERENCES design.templates (id) ON DELETE CASCADE,
    lighting_brand TEXT NOT NULL,
    plumbing_brand TEXT NOT NULL,
    toilet_brand TEXT NOT NULL,
    vanity_brand TEXT NOT NULL,
    vanity_storage TEXT NOT NULL
);

CREATE TABLE design.legacy_lookup (
    id VARCHAR(2) PRIMARY KEY,
    template_id UUID UNIQUE NOT NULL REFERENCES design.templates (id) ON DELETE CASCADE
);
-- migrate:down
DROP TRIGGER IF EXISTS refresh_templates_updated_at ON design.templates;
DROP TABLE IF EXISTS design.templates;
