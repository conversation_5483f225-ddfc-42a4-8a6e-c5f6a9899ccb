package entities

import (
	"net/url"
	"time"

	"github.com/google/uuid"
)

type RenditionStatus string

const (
	RenditionPending   RenditionStatus = "Pending"
	RenditionStarted   RenditionStatus = "Started"
	RenditionCompleted RenditionStatus = "Completed"
	RenditionOutdated  RenditionStatus = "Outdated"
	RenditionArchived  RenditionStatus = "Archived"
)

type Rendition struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	Status RenditionStatus `json:"status"`
	URL    url.URL         `json:"url"`
}
