package adapters

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type Preset struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	RoomLayout   json.RawMessage `json:"room_layout"`
	Measurements json.RawMessage `json:"measurements"`
	Design       Design          `json:"design"`
	Rendition    Rendition       `json:"rendition"`
}

func FromUsecasePreset(preset usecases.Preset) Preset {
	return Preset{
		Id:           preset.Id,
		CreatedAt:    preset.CreatedAt,
		UpdatedAt:    preset.UpdatedAt,
		RoomLayout:   preset.RoomLayout,
		Measurements: preset.Measurements,
		Design:       FromUsecaseDesign(preset.Design),
		Rendition:    FromDomainRendition(preset.Rendition),
	}
}
