SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: design; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA design;


--
-- Name: color_scheme_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.color_scheme_enum AS ENUM (
    'Neutral',
    'Bold'
);


--
-- Name: rendition_status_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);


--
-- Name: style_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);


--
-- Name: tile_pattern_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);


--
-- Name: http_img_url; Type: DOMAIN; Schema: public; Owner: -
--

CREATE DOMAIN public.http_img_url AS text
	CONSTRAINT http_img_url_check CHECK ((VALUE ~ '^https?://.*\.(webp|jpe?g|png)$'::text));


--
-- Name: posint; Type: DOMAIN; Schema: public; Owner: -
--

CREATE DOMAIN public.posint AS integer
	CONSTRAINT posint_check CHECK ((VALUE > 0));


--
-- Name: maintain_updated_at_column(); Type: FUNCTION; Schema: design; Owner: -
--

CREATE FUNCTION design.maintain_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: default_products; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.default_products (
    room_design_id uuid NOT NULL,
    floor_tile uuid,
    floor_tile_pattern design.tile_pattern_enum,
    paint uuid,
    toilet uuid,
    vanity uuid,
    faucet uuid,
    mirror uuid,
    lighting uuid,
    shelving uuid,
    wall_tile_placement text NOT NULL,
    wall_tile uuid,
    wall_tile_pattern design.tile_pattern_enum,
    wallpaper_placement text NOT NULL,
    wallpaper uuid,
    shower_system uuid,
    shower_floor_tile uuid,
    shower_floor_tile_pattern design.tile_pattern_enum,
    shower_wall_tile uuid,
    shower_wall_tile_pattern design.tile_pattern_enum,
    shower_short_wall_tile uuid,
    shower_glass uuid,
    niche_tile uuid,
    tub uuid,
    tub_filler uuid,
    tub_door uuid
);


--
-- Name: legacy_lookup; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.legacy_lookup (
    id character varying(2) NOT NULL,
    template_id uuid NOT NULL
);


--
-- Name: logs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_design_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    event jsonb NOT NULL
);


--
-- Name: render_prefs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.render_prefs (
    room_design_id uuid NOT NULL,
    shower_glass_visible boolean DEFAULT false NOT NULL,
    tub_door_visible boolean DEFAULT false NOT NULL,
    niches_visible boolean DEFAULT false NOT NULL
);


--
-- Name: renditions; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.renditions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_design_id uuid NOT NULL,
    room_layout_xxhash bytea NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status design.rendition_status_enum NOT NULL,
    url public.http_img_url,
    CONSTRAINT chk_rendition_url CHECK (((status <> 'Completed'::design.rendition_status_enum) OR (url IS NOT NULL)))
);


--
-- Name: retail_info; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.retail_info (
    room_design_id uuid NOT NULL,
    total_price_cents public.posint,
    lead_time_days public.posint,
    sku_count public.posint
);


--
-- Name: room_designs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.room_designs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    project_id text,
    status text,
    title text,
    description text,
    color_scheme design.color_scheme_enum,
    style design.style_enum
);


--
-- Name: template_options; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.template_options (
    template_id uuid NOT NULL,
    alcove_tub uuid NOT NULL,
    freestanding_tub uuid NOT NULL,
    shower_glass_fixed uuid NOT NULL,
    shower_glass_sliding uuid NOT NULL,
    shower_system_full uuid NOT NULL,
    shower_system_shower uuid NOT NULL,
    tub_door_fixed uuid NOT NULL,
    tub_door_sliding uuid NOT NULL
);


--
-- Name: template_provenance; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.template_provenance (
    template_id uuid NOT NULL,
    lighting_brand text NOT NULL,
    plumbing_brand text NOT NULL,
    toilet_brand text NOT NULL,
    vanity_brand text NOT NULL,
    vanity_storage text NOT NULL
);


--
-- Name: templates; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.templates (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    color_scheme design.color_scheme_enum NOT NULL,
    style design.style_enum NOT NULL,
    render_priority public.posint,
    name text NOT NULL,
    description text NOT NULL,
    atmosphere text[] NOT NULL,
    color_palette text[] NOT NULL,
    material_palette text[] NOT NULL,
    inspiration text NOT NULL,
    highlighted_brand_urls public.http_img_url[],
    rendition_id uuid NOT NULL
);


--
-- Name: vanity_scaling_options; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.vanity_scaling_options (
    template_id uuid NOT NULL,
    min_vanity_length_inches public.posint NOT NULL,
    vanity_product_id uuid NOT NULL,
    faucet_product_id uuid NOT NULL
);


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: default_products default_products_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_pkey PRIMARY KEY (room_design_id);


--
-- Name: legacy_lookup legacy_lookup_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.legacy_lookup
    ADD CONSTRAINT legacy_lookup_pkey PRIMARY KEY (id);


--
-- Name: legacy_lookup legacy_lookup_template_id_key; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.legacy_lookup
    ADD CONSTRAINT legacy_lookup_template_id_key UNIQUE (template_id);


--
-- Name: logs logs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.logs
    ADD CONSTRAINT logs_pkey PRIMARY KEY (id);


--
-- Name: render_prefs render_prefs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_pkey PRIMARY KEY (room_design_id);


--
-- Name: renditions renditions_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.renditions
    ADD CONSTRAINT renditions_pkey PRIMARY KEY (id);


--
-- Name: retail_info retail_info_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_pkey PRIMARY KEY (room_design_id);


--
-- Name: room_designs room_designs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.room_designs
    ADD CONSTRAINT room_designs_pkey PRIMARY KEY (id);


--
-- Name: template_options template_options_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.template_options
    ADD CONSTRAINT template_options_pkey PRIMARY KEY (template_id);


--
-- Name: template_provenance template_provenance_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.template_provenance
    ADD CONSTRAINT template_provenance_pkey PRIMARY KEY (template_id);


--
-- Name: templates templates_name_key; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.templates
    ADD CONSTRAINT templates_name_key UNIQUE (name);


--
-- Name: templates templates_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.templates
    ADD CONSTRAINT templates_pkey PRIMARY KEY (id);


--
-- Name: templates templates_rendition_id_key; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.templates
    ADD CONSTRAINT templates_rendition_id_key UNIQUE (rendition_id);


--
-- Name: vanity_scaling_options vanity_scaling_options_template_id_min_vanity_length_inches_key; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.vanity_scaling_options
    ADD CONSTRAINT vanity_scaling_options_template_id_min_vanity_length_inches_key UNIQUE (template_id, min_vanity_length_inches);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: logs_room_design_id_idx; Type: INDEX; Schema: design; Owner: -
--

CREATE INDEX logs_room_design_id_idx ON design.logs USING btree (room_design_id);


--
-- Name: renditions_room_design_id_idx; Type: INDEX; Schema: design; Owner: -
--

CREATE INDEX renditions_room_design_id_idx ON design.renditions USING btree (room_design_id);


--
-- Name: room_designs_project_id_idx; Type: INDEX; Schema: design; Owner: -
--

CREATE INDEX room_designs_project_id_idx ON design.room_designs USING btree (project_id);


--
-- Name: room_designs refresh_designs_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_designs_updated_at BEFORE UPDATE ON design.room_designs FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();


--
-- Name: renditions refresh_renditions_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_renditions_updated_at BEFORE UPDATE ON design.renditions FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();


--
-- Name: templates refresh_templates_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_templates_updated_at BEFORE UPDATE ON design.templates FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();


--
-- Name: default_products default_products_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: legacy_lookup legacy_lookup_template_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.legacy_lookup
    ADD CONSTRAINT legacy_lookup_template_id_fkey FOREIGN KEY (template_id) REFERENCES design.templates(id) ON DELETE CASCADE;


--
-- Name: logs logs_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.logs
    ADD CONSTRAINT logs_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id);


--
-- Name: render_prefs render_prefs_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: renditions renditions_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.renditions
    ADD CONSTRAINT renditions_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: retail_info retail_info_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: template_options template_options_template_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.template_options
    ADD CONSTRAINT template_options_template_id_fkey FOREIGN KEY (template_id) REFERENCES design.templates(id) ON DELETE CASCADE;


--
-- Name: template_provenance template_provenance_template_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.template_provenance
    ADD CONSTRAINT template_provenance_template_id_fkey FOREIGN KEY (template_id) REFERENCES design.templates(id) ON DELETE CASCADE;


--
-- Name: templates templates_rendition_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.templates
    ADD CONSTRAINT templates_rendition_id_fkey FOREIGN KEY (rendition_id) REFERENCES design.renditions(id) ON DELETE CASCADE;


--
-- Name: vanity_scaling_options vanity_scaling_options_template_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.vanity_scaling_options
    ADD CONSTRAINT vanity_scaling_options_template_id_fkey FOREIGN KEY (template_id) REFERENCES design.templates(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


--
-- Dbmate schema migrations
--

INSERT INTO public.schema_migrations (version) VALUES
    ('20250618235404'),
    ('20250619143144'),
    ('20250625085404'),
    ('20250625090429');
