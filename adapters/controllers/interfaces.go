package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type storage interface {
	IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error)
	DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, []error, error)
	UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error
	MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error
	GetCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId) (string, error)
	UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error
}

type textGenerator interface {
	GenerateDesignTitleAndDescription(ctx context.Context, design usecases.Design) (string, string, error)
}
