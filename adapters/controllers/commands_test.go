package controllers_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

// TODO: move memStore to a separate package that can be shared with other tests.
type project struct {
	designs         []adapters.Design
	currentDesignId string
}

type memStore struct {
	projects map[entities.ProjectId]*project
}

func newMemStore() *memStore {
	m := memStore{
		projects: make(map[entities.ProjectId]*project),
	}
	m.projects[projId] = &project{}
	return &m
}

func (m *memStore) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	results, ok := m.projects[projectId]
	if !ok {
		fmt.Printf("Project %s not found in store", projectId)
		return nil, fmt.Errorf("project %s not found", projectId)
	}
	return results.designs, nil
}

func (m *memStore) DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, []error, error) {
	results := make(map[entities.ProjectId][]adapters.Design)
	errors := []error{}
	for _, projectId := range projectIds {
		designs, err := m.DesignsForProject(ctx, projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (m *memStore) addDesignToProject(projectId entities.ProjectId, design adapters.Design) error {
	project := m.projects[projectId]
	project.designs = append(project.designs, design)
	return nil
}

func (m *memStore) UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error {
	project, ok := m.projects[projectId]
	if !ok {
		return fmt.Errorf("project %s not found", projectId)
	}
	project.designs = designs
	return nil
}

func (m *memStore) MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error {
	return nil
}

func (m *memStore) IdsOfProjectsWithDesigns(_ context.Context) ([]entities.ProjectId, error) {
	var projectIds []entities.ProjectId
	for projectId := range m.projects {
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

func (m *memStore) GetCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId) (string, error) {
	proj, ok := m.projects[projectId]
	if !ok {
		return "", fmt.Errorf("no current design ID set for project %s", projectId)
	}
	return proj.currentDesignId, nil
}

func (m *memStore) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	proj, ok := m.projects[projectId]
	if !ok {
		return fmt.Errorf("no current design ID set for project %s", projectId)
	}
	proj.currentDesignId = designId.String()
	return nil
}

const (
	projId             = "PRJ-FOOBAR"
	jsonSchemaFilename = "../../room-design.schema.json"
)

func setup(t *testing.T) (*controllers.DesignMutationController, *memStore, adapters.Design) {
	t.Helper()
	m := newMemStore()
	ai := gateways.NewFakeLLM()
	handler := controllers.NewDesignMutationController(m, ai, nil)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	if err := m.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	// Set a default current design ID for SaveAllDesignsForProject tests
	m.projects[projId].currentDesignId = testDesign.ID
	return handler, m, testDesign
}

func TestAddingDesign(t *testing.T) {
	handler, store, _ := setup(t)
	testDesign2 := genDesign()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.CreateDesign(t.Context(), projId, testDesign2, presenter)
	if status := recorder.Code; status != http.StatusCreated {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	locationHeader := recorder.Header().Get("Location")
	if locationHeader == "" {
		t.Error("Location header not set")
	} else {
		expectedLocationPrefix := fmt.Sprintf("/projects/%s/designs/", projId)
		if !strings.HasPrefix(locationHeader, expectedLocationPrefix) {
			t.Errorf("Location header prefix mismatch: %s should begin with %s", locationHeader, expectedLocationPrefix)
		}
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, 2)
	}
}

func TestModifyingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	testDesign.Tags = 1
	vis := true
	testDesign.IsShowerGlassVisible = &vis
	patch := adapters.Design{
		ID:                   testDesign.ID,
		WallpaperPlacement:   &vanityWall,
		Tags:                 1,
		IsShowerGlassVisible: &vis,
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.ModifyDesign(t.Context(), projId, patch, presenter)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}
func TestReplacingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveDesign(t.Context(), projId, testDesign, presenter)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}

func TestDeletingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.DeleteDesign(t.Context(), projId, uuid.MustParse(testDesign.ID), presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 0; got != want {
		t.Errorf("wrong number of designs: got %d want %d", got, want)
	}
}

func TestSaveAllDesignsForProject_WithValidDesigns(t *testing.T) {
	handler, store, _ := setup(t)

	// Create test designs with valid UUIDs
	design1 := genDesign()
	design1.ID = uuid.NewString()
	design2 := genDesign()
	design2.ID = uuid.NewString()
	design3 := genDesign()
	design3.ID = uuid.NewString()

	designs := []adapters.Design{design1, design2, design3}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify designs were saved
	savedDesigns, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if got, want := len(savedDesigns), 3; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}

	// Verify design IDs are preserved
	for i, design := range savedDesigns {
		if design.ID != designs[i].ID {
			t.Errorf("design ID mismatch at index %d: got %s want %s", i, design.ID, designs[i].ID)
		}
	}
}

func TestSaveAllDesignsForProject_WithShortIDs_GeneratesNewUUIDs(t *testing.T) {
	handler, store, _ := setup(t)

	// Create test designs with short IDs (< 3 characters)
	design1 := genDesign()
	design1.ID = "a" // Short ID
	design2 := genDesign()
	design2.ID = "bb" // Short ID
	design3 := genDesign()
	design3.ID = uuid.NewString() // Valid UUID

	designs := []adapters.Design{design1, design2, design3}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify designs were saved
	savedDesigns, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if got, want := len(savedDesigns), 3; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}

	// Verify short IDs were replaced with UUIDs
	for i, design := range savedDesigns {
		if i < 2 { // First two designs had short IDs
			if design.ID == designs[i].ID {
				t.Errorf("short ID was not replaced at index %d: still has ID %s", i, design.ID)
			}
			if _, err := uuid.Parse(design.ID); err != nil {
				t.Errorf("generated ID is not a valid UUID at index %d: %s", i, design.ID)
			}
		} else { // Third design had valid UUID
			if design.ID != designs[i].ID {
				t.Errorf("valid UUID was changed at index %d: got %s want %s", i, design.ID, designs[i].ID)
			}
		}
	}
}

func TestSaveAllDesignsForProject_UpdatesCurrentDesignId_WhenCurrentDesignHasShortId(t *testing.T) {
	handler, store, _ := setup(t)

	// Set up a current design ID that matches one of our short IDs
	currentDesignId := "xy"
	store.projects[projId].currentDesignId = currentDesignId

	// Create test designs where one has the same short ID as current design
	design1 := genDesign()
	design1.ID = "xy" // Matches current design ID
	design2 := genDesign()
	design2.ID = uuid.NewString() // Valid UUID

	designs := []adapters.Design{design1, design2}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify designs were saved
	savedDesigns, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if got, want := len(savedDesigns), 2; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}

	// Verify the short ID was replaced with a UUID
	if savedDesigns[0].ID == "xy" {
		t.Error("short ID was not replaced with UUID")
	}
	if _, err := uuid.Parse(savedDesigns[0].ID); err != nil {
		t.Errorf("generated ID is not a valid UUID: %s", savedDesigns[0].ID)
	}

	// Verify current design ID was updated to the new UUID
	newCurrentDesignId, err := store.GetCurrentDesignIdForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if newCurrentDesignId == currentDesignId {
		t.Error("current design ID was not updated")
	}

	if newCurrentDesignId != savedDesigns[0].ID {
		t.Errorf("current design ID mismatch: got %s want %s", newCurrentDesignId, savedDesigns[0].ID)
	}
}

func TestSaveAllDesignsForProject_DoesNotUpdateCurrentDesignId_WhenNoMatch(t *testing.T) {
	handler, store, _ := setup(t)

	// Set up a current design ID that doesn't match any of our designs
	currentDesignId := "different"
	store.projects[projId].currentDesignId = currentDesignId

	// Create test designs with short IDs that don't match current design
	design1 := genDesign()
	design1.ID = "xy" // Different from current design ID
	design2 := genDesign()
	design2.ID = "ab" // Different from current design ID

	designs := []adapters.Design{design1, design2}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify current design ID was not changed
	newCurrentDesignId, err := store.GetCurrentDesignIdForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if newCurrentDesignId != currentDesignId {
		t.Errorf("current design ID was unexpectedly changed: got %s want %s", newCurrentDesignId, currentDesignId)
	}
}

func TestSaveAllDesignsForProject_HandlesGetCurrentDesignIdError(t *testing.T) {
	const defaultDesignId = "01"
	// Create a fresh store without setup to avoid setting current design ID
	m := newMemStore()
	m.projects[projId] = &project{}
	ai := gateways.NewFakeLLM()
	handler := controllers.NewDesignMutationController(m, ai, nil)

	testDesign := genDesign()
	testDesign.ID = defaultDesignId
	designs := []adapters.Design{testDesign}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify designs were saved
	savedDesigns, err := m.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(savedDesigns), 1; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}

	// Verify the short ID was replaced with a UUID
	if savedDesigns[0].ID == defaultDesignId {
		t.Error("short ID was not replaced with UUID")
	}
	if _, err := uuid.Parse(savedDesigns[0].ID); err != nil {
		t.Errorf("generated ID is not a valid UUID: %s", savedDesigns[0].ID)
	}

	// Verify current design ID was updated to the new UUID
	newCurrentDesignId, err := m.GetCurrentDesignIdForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if newCurrentDesignId == "" {
		t.Error("current design ID was not set")
	}
	if newCurrentDesignId != savedDesigns[0].ID {
		t.Errorf("current design ID mismatch: got %s want %s", newCurrentDesignId, savedDesigns[0].ID)
	}
}

func TestSaveAllDesignsForProject_HandlesNilPresenter(t *testing.T) {
	handler, _, _ := setup(t)

	designs := []adapters.Design{genDesign()}

	// Should panic with nil presenter
	defer func() {
		if r := recover(); r == nil {
			t.Error("expected panic with nil presenter")
		}
	}()

	handler.SaveAllDesignsForProject(context.Background(), projId, designs, nil)
}

func TestSaveAllDesignsForProject_HandlesEmptyDesignsList(t *testing.T) {
	handler, store, _ := setup(t)

	// Set up current design ID
	store.projects[projId].currentDesignId = "some-id"

	designs := []adapters.Design{} // Empty list

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify empty list was saved
	savedDesigns, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if got, want := len(savedDesigns), 0; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}
}

// Test edge case where design ID is exactly 3 characters (boundary condition)
func TestSaveAllDesignsForProject_WithThreeCharacterID_DoesNotGenerateNewUUID(t *testing.T) {
	handler, store, _ := setup(t)

	// Create test design with exactly 3 characters (should not be replaced)
	design1 := genDesign()
	design1.ID = "abc" // Exactly 3 characters

	designs := []adapters.Design{design1}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

	handler.SaveAllDesignsForProject(t.Context(), projId, designs, presenter)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify designs were saved
	savedDesigns, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}

	if got, want := len(savedDesigns), 1; got != want {
		t.Errorf("wrong number of designs saved: got %d want %d", got, want)
	}

	// Verify 3-character ID was NOT replaced
	if savedDesigns[0].ID != "abc" {
		t.Errorf("3-character ID was unexpectedly replaced: got %s want abc", savedDesigns[0].ID)
	}
}
