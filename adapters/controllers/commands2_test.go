package controllers_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func setup2(t *testing.T) (*controllers.DesignWriteController, *gateways.FakeRelDb) {
	t.Helper()
	r := gateways.NewFakeRelDb()
	ai := gateways.NewFakeLLM()
	designCreator := usecases.NewDesignCreater(r, ai, nil)
	designUpdater := usecases.NewDesignUpdater(r)
	designSaver := usecases.NewDesignSaver(r)
	bulkDesignSaver := usecases.NewBulkDesignSaver(r, nil)
	designDeleter := usecases.NewDesignDeleter(r)
	ctlr := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, bulkDesignSaver, designDeleter, nil)
	return ctlr, r
}

func TestAddingNewDesign(t *testing.T) {
	ctlr, store := setup2(t)
	testDesign := genDesign()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	ctlr.SaveDesign(t.Context(), projId, testDesign, presenter)
	require.EqualValues(t, recorder.Code, http.StatusCreated)
	locationHeader := recorder.Header().Get("Location")
	require.NotEmpty(t, locationHeader)
	expectedLocationPrefix := fmt.Sprintf("/projects/%s/designs/", projId)
	if !strings.HasPrefix(locationHeader, expectedLocationPrefix) {
		t.Errorf("Location header prefix mismatch: %s should begin with %s", locationHeader, expectedLocationPrefix)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	require.Len(t, designs, 1)
	got := designs[0]
	testDesign.ID = got.ID.String()
	want, err := testDesign.ToUsecaseDesign(projId)
	require.NoError(t, err)
	want.Created = got.Created
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func mkDesign(t *testing.T, ctlr *controllers.DesignWriteController) uuid.UUID {
	t.Helper()
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	ctlr.SaveDesign(t.Context(), projId, testDesign, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	return uuid.MustParse(testDesign.ID)
}

func TestReplacingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	testDesign := genDesign()
	testDesign.ID = designId.String()
	toiletUUID := uuid.NewString()
	testDesign.Toilet = &toiletUUID
	want, err := testDesign.ToUsecaseDesign(projId)
	require.NoError(t, err)
	want.LastUpdated = time.Now()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveDesign(t.Context(), projId, testDesign, presenter)
	require.EqualValues(t, recorder.Code, http.StatusNoContent)
	got, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	want.Created = got.Created
	assert.Greater(t, got.LastUpdated, want.LastUpdated)
	want.LastUpdated = got.LastUpdated
	assert.Equal(t, want, got)
}

func TestModifyingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	design, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	toiletUUID := uuid.New()
	design.Toilet = &toiletUUID
	toilet := toiletUUID.String()
	patch := adapters.Design{ID: design.ID.String(), Toilet: &toilet}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.ModifyDesign(t.Context(), patch, presenter)
	require.EqualValues(t, recorder.Code, http.StatusNoContent)
	got, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	assert.Greater(t, got.LastUpdated, design.LastUpdated)
	design.LastUpdated = got.LastUpdated
	assert.Equal(t, design, got)
}

func TestDeletingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.DeleteDesign(t.Context(), designId, presenter)
	require.EqualValues(t, recorder.Code, http.StatusNoContent)
	_, err := store.ReadDesign(context.Background(), designId)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}
