package controllers

import (
	"context"
	"log/slog"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignWriteController struct {
	maker     *usecases.DesignCreater
	changer   *usecases.DesignUpdater
	saver     *usecases.DesignSaver
	bulkAdder *usecases.BulkDesignSaver
	eraser    *usecases.DesignDeleter
	logger    *slog.Logger
}

func NewDesignWriteController(
	maker *usecases.DesignCreater, changer *usecases.DesignUpdater, saver *usecases.DesignSaver, bulkAdder *usecases.BulkDesignSaver, eraser *usecases.DesignDeleter,
	logger *slog.Logger) *DesignWriteController {
	if maker == nil {
		panic("maker cannot be nil")
	}
	if changer == nil {
		panic("changer cannot be nil")
	}
	if saver == nil {
		panic("saver cannot be nil")
	}
	if bulkAdder == nil {
		panic("bulkAdder cannot be nil")
	}
	if eraser == nil {
		panic("eraser cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignWriteController{maker: maker, changer: changer, saver: saver, bulkAdder: bulkAdder, eraser: eraser, logger: logger}
}

func (h *DesignWriteController) SaveDesign(ctx context.Context, projectId entities.ProjectId, design adapters.Design, presenter usecases.DesignMutationOutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	useCase := usecases.Created
	if len(design.ID) > 2 {
		useCase = usecases.Updated
	} else {
		design.ID = uuid.NewString()
	}
	d, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	d.LastUpdated = time.Now()
	if useCase == usecases.Updated {
		h.saver.SaveDesign(ctx, presenter, d)
		return
	}
	h.maker.CreateDesign(ctx, presenter, d)
}

func (h *DesignWriteController) ModifyDesign(ctx context.Context, design adapters.Design, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if design.ID == "" {
		h.logger.ErrorContext(ctx, "Attempt to modify design with no ID")
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	d, err := design.ToUsecaseDesignDiff()
	if err != nil {
		presenter.PresentError(err)
		return
	}
	h.changer.UpdateDesign(ctx, presenter, d)
}

func (h *DesignWriteController) SaveDesignsForProject(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	data := make([]usecases.Design, len(designs))
	for i, d := range designs {
		design, err := d.ToUsecaseDesign(projectId)
		if err != nil {
			presenter.PresentError(err)
			return
		}
		data[i] = design
	}
	h.bulkAdder.SaveDesigns(ctx, presenter, data)
}

func (h *DesignWriteController) DeleteDesign(ctx context.Context, designId uuid.UUID, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	h.eraser.DeleteDesign(ctx, presenter, designId)
}
