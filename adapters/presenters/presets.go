package presenters

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

const defaultMeasurements = `{
  "ceilingArea": 64.89221,
  "floorArea": 50.3027878,
  "halfWallTileLength": 306.588379,
  "linearLengthOfWall": 276.567932,
  "nichesArea": 10.4444237,
  "showerAreaHeight": 75.66934,
  "showerWallArea": 58.05525,
  "tubLength": 58.36333,
  "vanityHalfWallArea": 36.19948,
  "vanityLength": 78.5976,
  "vanityWallArea": 82.4565048,
  "vanityWallLength": 124.112495,
  "wallHalfArea": 89.42161,
  "wallPaintArea": 203.687836
}`

type PresetPresenter struct {
	w                 http.ResponseWriter
	logger            *slog.Logger
	defaultRoomLayout json.RawMessage
}

func NewPresetPresenter(w http.ResponseWriter, logger *slog.Logger, defaultRoomLayout json.RawMessage) PresetPresenter {
	return PresetPresenter{w: w, logger: logger, defaultRoomLayout: defaultRoomLayout}
}

func (dp PresetPresenter) PresentError(err error) {
	http.Error(dp.w, err.Error(), http.StatusNotFound)
}

func (pp PresetPresenter) PresentData(ctx context.Context, data any) {
	bytes, err := json.Marshal(data)
	if err != nil {
		pp.logger.ErrorContext(ctx, "Could not marshal preset data into JSON",
			slog.String("error", err.Error()))
		http.Error(pp.w, err.Error(), http.StatusInternalServerError)
		return
	}
	pp.w.Header().Set("Access-Control-Allow-Origin", "*")
	pp.w.Header().Set("Content-Type", "application/json")
	if _, err = pp.w.Write(bytes); err != nil {
		http.Error(pp.w, err.Error(), http.StatusInternalServerError)
	}
}

func (pp PresetPresenter) PresentPreset(ctx context.Context, preset usecases.Preset) {
	if preset.RoomLayout == nil {
		preset.RoomLayout = pp.defaultRoomLayout
	}
	preset.Measurements = json.RawMessage(defaultMeasurements)
	p := adapters.FromUsecasePreset(preset)
	pp.PresentData(ctx, p)
}
