package presenters_test

import (
	"context"
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"
	"unicode"

	"github.com/google/uuid"
	"github.com/leanovate/gopter"
	"github.com/leanovate/gopter/gen"
	"github.com/leanovate/gopter/prop"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// Property-based tests for presenter functions

func TestUsePascalCaseTilePatterns_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: capitalizing a string should always result in the first character being title case if it's a letter
	properties.Property("first character is title case after capitalization", prop.ForAll(
		func(s string) bool {
			if len(s) == 0 {
				return true // Empty strings remain empty
			}

			pattern := usecases.TilePattern(s)
			design := adapters.Design{
				FloorTilePattern: &pattern,
			}

			result := presenters.UsePascalCaseTilePatterns(design)

			if result.FloorTilePattern == nil {
				return false
			}

			resultStr := string(*result.FloorTilePattern)
			if len(resultStr) == 0 {
				return len(s) == 0
			}

			originalRunes := []rune(s)
			resultRunes := []rune(resultStr)

			if len(originalRunes) == 0 || len(resultRunes) == 0 {
				return len(originalRunes) == len(resultRunes)
			}

			firstOriginal := originalRunes[0]
			firstResult := resultRunes[0]

			// If the original character is a letter, the result should be its title case
			if unicode.IsLetter(firstOriginal) {
				return firstResult == unicode.ToTitle(firstOriginal)
			}
			// If not a letter, it should remain unchanged
			return firstResult == firstOriginal
		},
		gen.AlphaString(),
	))

	// Property: capitalizing should not change the length of the string
	properties.Property("length is preserved", prop.ForAll(
		func(s string) bool {
			pattern := usecases.TilePattern(s)
			design := adapters.Design{
				FloorTilePattern: &pattern,
			}

			result := presenters.UsePascalCaseTilePatterns(design)

			if result.FloorTilePattern == nil {
				return pattern == usecases.TilePattern("")
			}

			return len(string(*result.FloorTilePattern)) == len(s)
		},
		gen.AnyString(),
	))

	// Property: capitalizing twice should be idempotent
	properties.Property("capitalization is idempotent", prop.ForAll(
		func(s string) bool {
			pattern := usecases.TilePattern(s)
			design := adapters.Design{
				FloorTilePattern: &pattern,
			}

			result1 := presenters.UsePascalCaseTilePatterns(design)
			result2 := presenters.UsePascalCaseTilePatterns(result1)

			if result1.FloorTilePattern == nil && result2.FloorTilePattern == nil {
				return true
			}
			if result1.FloorTilePattern == nil || result2.FloorTilePattern == nil {
				return false
			}

			return *result1.FloorTilePattern == *result2.FloorTilePattern
		},
		gen.AnyString(),
	))

	// Property: nil patterns remain nil
	properties.Property("nil patterns remain nil", prop.ForAll(
		func() bool {
			design := adapters.Design{
				FloorTilePattern:       nil,
				ShowerFloorTilePattern: nil,
				ShowerWallTilePattern:  nil,
				WallTilePattern:        nil,
			}

			result := presenters.UsePascalCaseTilePatterns(design)

			return result.FloorTilePattern == nil &&
				result.ShowerFloorTilePattern == nil &&
				result.ShowerWallTilePattern == nil &&
				result.WallTilePattern == nil
		},
	))

	properties.TestingRun(t)
}

func TestPresentData_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: presenting valid JSON data should always succeed
	properties.Property("valid JSON data always succeeds", prop.ForAll(
		func(s string, i int, b bool) bool {
			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignsPresenter(CreateSilentLogger(), recorder)

			// Create a simple struct that can always be marshaled
			data := struct {
				String string `json:"string"`
				Int    int    `json:"int"`
				Bool   bool   `json:"bool"`
			}{
				String: s,
				Int:    i,
				Bool:   b,
			}

			presenter.PresentData(context.Background(), data)

			// Should always succeed with valid data
			return recorder.Code == 200
		},
		gen.AnyString(),
		gen.Int(),
		gen.Bool(),
	))

	// Property: CORS headers are always set
	properties.Property("CORS headers always set", prop.ForAll(
		func(data interface{}) bool {
			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignsPresenter(CreateSilentLogger(), recorder)

			presenter.PresentData(context.Background(), data)

			return recorder.Header().Get("Access-Control-Allow-Origin") == "*"
		},
		gen.AnyString(),
	))

	properties.TestingRun(t)
}

func TestPresentDesigns_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: presenting any slice of designs should result in valid JSON array
	properties.Property("always produces valid JSON array", prop.ForAll(
		func(designCount int) bool {
			if designCount < 0 || designCount > 100 { // Limit to reasonable size
				return true
			}

			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignsPresenter(CreateSilentLogger(), recorder)

			// Generate designs
			designs := make([]usecases.Design, designCount)
			for i := 0; i < designCount; i++ {
				designs[i] = GenerateTestUsecaseDesign("PRJ-TEST")
			}

			presenter.PresentDesigns(context.Background(), designs)

			// Should always succeed and produce valid JSON
			if recorder.Code != 200 {
				return false
			}

			var result []interface{}
			err := json.Unmarshal(recorder.Body.Bytes(), &result)
			return err == nil && len(result) == designCount
		},
		gen.IntRange(0, 10), // Keep it small for performance
	))

	properties.TestingRun(t)
}

func TestPresentError_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: any non-nil error should result in 404 status
	properties.Property("non-nil errors result in 404", prop.ForAll(
		func(errorMsg string) bool {
			if errorMsg == "" {
				errorMsg = "test error" // Ensure non-empty error
			}

			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignsPresenter(CreateSilentLogger(), recorder)

			err := &customError{message: errorMsg}
			presenter.PresentError(err)

			return recorder.Code == 404
		},
		gen.AnyString().SuchThat(func(s string) bool { return s != "" }),
	))

	properties.TestingRun(t)
}

// Custom error type for property testing
type customError struct {
	message string
}

func (e *customError) Error() string {
	return e.message
}

func TestMutationOutcomePresenter_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: ConveySuccessWithResource always sets CORS headers
	properties.Property("always sets CORS headers", prop.ForAll(
		func(projectSuffix string, useEmptyProject, useEmptyDesign bool) bool {
			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignMutationOutcomePresenter(CreateSilentLogger(), recorder)

			var projectId entities.ProjectId
			var designId uuid.UUID

			if !useEmptyProject {
				pid := entities.ProjectId("PRJ-" + projectSuffix)
				projectId = pid
			}

			if !useEmptyDesign {
				did := uuid.New()
				designId = did
			}

			design := usecases.Design{
				ID:        designId,
				ProjectID: projectId,
			}
			presenter.ConveySuccessWithResource(design, usecases.Created)

			return recorder.Header().Get("Access-Control-Allow-Origin") == "*"
		},
		gen.AnyString(),
		gen.Bool(),
		gen.Bool(),
	))

	// Property: Location header format is consistent
	properties.Property("location header format is consistent", prop.ForAll(
		func(projectSuffix string) bool {
			if strings.TrimSpace(projectSuffix) == "" {
				return true // Skip empty project suffixes
			}

			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignMutationOutcomePresenter(CreateSilentLogger(), recorder)

			projectId := entities.ProjectId("PRJ-" + projectSuffix)
			designId := uuid.New()
			design := usecases.Design{
				ID:        designId,
				ProjectID: projectId,
			}

			presenter.ConveySuccessWithResource(design, usecases.Created)

			location := recorder.Header().Get("Location")
			expectedPrefix := "/projects/PRJ-" + projectSuffix + "/designs/"

			return strings.HasPrefix(location, expectedPrefix) &&
				strings.HasSuffix(location, designId.String())
		},
		gen.AnyString().SuchThat(func(s string) bool { return strings.TrimSpace(s) != "" }),
	))

	properties.TestingRun(t)
}

// Generator for valid tile patterns
func genTilePattern() gopter.Gen {
	return func(genParams *gopter.GenParameters) *gopter.GenResult {
		patterns := []usecases.TilePattern{
			usecases.HorizontalStacked,
			usecases.VerticalStacked,
			usecases.HalfOffset,
			usecases.ThirdOffset,
			usecases.Herringbone,
		}

		// 20% chance of nil, 80% chance of a valid pattern
		if genParams.Rng.Intn(5) == 0 {
			return gopter.NewGenResult((*usecases.TilePattern)(nil), gopter.NoShrinker)
		}

		idx := genParams.Rng.Intn(len(patterns))
		pattern := patterns[idx]
		return gopter.NewGenResult(&pattern, gopter.NoShrinker)
	}
}

func TestTilePatternHandling_Properties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: valid tile patterns are preserved in their essence
	properties.Property("valid tile patterns preserve meaning", prop.ForAll(
		func(pattern *usecases.TilePattern) bool {
			design := adapters.Design{
				FloorTilePattern: pattern,
			}

			result := presenters.UsePascalCaseTilePatterns(design)

			// If input was nil, output should be nil
			if pattern == nil {
				return result.FloorTilePattern == nil
			}

			// If input was empty, output should be empty
			if len(*pattern) == 0 {
				return result.FloorTilePattern != nil && len(*result.FloorTilePattern) == 0
			}

			// Otherwise, the result should have the same content except for case
			return result.FloorTilePattern != nil &&
				strings.EqualFold(string(*pattern), string(*result.FloorTilePattern))
		},
		genTilePattern(),
	))

	properties.TestingRun(t)
}
